# Property Management Automation - Data & Logic Plan

This document outlines the data models, attributes, associations, and core business logic for the Property Management Automation Platform, designed for a Ruby on Rails 8 application using PostgreSQL.

---

## Core Models

### 1. `User`
The central model for landlords and tenants.

- **Attributes:**
  - `id` (Primary Key)
  - `email` (string, unique, indexed)
  - `password_digest` (string)
  - `first_name` (string)
  - `last_name` (string)
  - `role` (enum: `landlord`, `tenant`, `admin`, default: `tenant`)
  - `created_at`, `updated_at`

- **Associations:**
  - `has_many :properties, foreign_key: 'landlord_id', dependent: :destroy`
  - `has_many :leases, foreign_key: 'tenant_id', dependent: :destroy`
  - `has_many :maintenance_requests, foreign_key: 'requester_id'`

### 2. `Property`
A building or piece of real estate owned by a landlord.

- **Attributes:**
  - `id` (Primary Key)
  - `landlord_id` (Foreign Key to `User`, indexed)
  - `name` (string, e.g., "Downtown Apartments")
  - `address` (string)
  - `city` (string)
  - `state` (string)
  - `zip_code` (string)
  - `property_type` (enum: `apartment_complex`, `single_family_home`, `duplex`)
  - `created_at`, `updated_at`

- **Associations:**
  - `belongs_to :landlord, class_name: 'User'`
  - `has_many :units, dependent: :destroy`
  - `has_many :expenses, dependent: :destroy`

### 3. `Unit`
An individual rentable unit within a property.

- **Attributes:**
  - `id` (Primary Key)
  - `property_id` (Foreign Key to `Property`, indexed)
  - `unit_number` (string, e.g., "Apt 101")
  - `bedrooms` (integer)
  - `bathrooms` (decimal)
  - `square_footage` (integer)
  - `is_occupied` (boolean, default: false, indexed)
  - `created_at`, `updated_at`

- **Associations:**
  - `belongs_to :property`
  - `has_one :active_lease, -> { where(status: 'active') }, class_name: 'Lease'`
  - `has_many :leases`
  - `has_many :maintenance_requests`

### 4. `Lease`
The legal agreement binding a tenant to a unit.

- **Attributes:**
  - `id` (Primary Key)
  - `unit_id` (Foreign Key to `Unit`, indexed)
  - `tenant_id` (Foreign Key to `User`, indexed)
  - `start_date` (date)
  - `end_date` (date)
  - `monthly_rent` (decimal)
  - `security_deposit` (decimal)
  - `status` (enum: `pending`, `active`, `expired`, `terminated`, indexed)
  - `created_at`, `updated_at`

- **Associations:**
  - `belongs_to :unit`
  - `belongs_to :tenant, class_name: 'User'`
  - `has_many :rent_payments, dependent: :destroy`

### 5. `RentPayment`
A record of a single rent payment transaction.

- **Attributes:**
  - `id` (Primary Key)
  - `lease_id` (Foreign Key to `Lease`, indexed)
  - `amount_due` (decimal)
  - `amount_paid` (decimal, nullable)
  - `due_date` (date)
  - `paid_at` (datetime, nullable)
  - `status` (enum: `due`, `paid`, `late`, `partially_paid`)
  - `stripe_payment_intent_id` (string, indexed)
  - `created_at`, `updated_at`

- **Associations:**
  - `belongs_to :lease`

### 6. `MaintenanceRequest`
A request for repairs or service submitted by a tenant.

- **Attributes:**
  - `id` (Primary Key)
  - `unit_id` (Foreign Key to `Unit`, indexed)
  - `requester_id` (Foreign Key to `User`, indexed)
  - `title` (string)
  - `description` (text)
  - `category` (enum: `plumbing`, `electrical`, `appliance`, `other`)
  - `urgency` (enum: `low`, `medium`, `high`)
  - `status` (enum: `submitted`, `in_progress`, `completed`, `closed`)
  - `created_at`, `updated_at`

- **Associations:**
  - `belongs_to :unit`
  - `belongs_to :requester, class_name: 'User'`
  - `has_many_attached :photos` (using Active Storage)

### 7. `Expense`
A record of an expense associated with a property, for landlord bookkeeping.

- **Attributes:**
  - `id` (Primary Key)
  - `property_id` (Foreign Key to `Property`, indexed)
  - `description` (string)
  - `amount` (decimal)
  - `date` (date)
  - `category` (enum: `repairs`, `utilities`, `mortgage`, `taxes`)
  - `created_at`, `updated_at`

- **Associations:**
  - `belongs_to :property`

---

## Core Business Logic

1.  **Tenant Onboarding & Leasing:**
    - A landlord invites a prospective tenant via email.
    - The tenant signs up, creating a `User` with the `tenant` role.
    - The landlord creates a `Lease` record, associating the `tenant` with a `Unit`.
    - The `Lease` status starts as `pending`. Once the `start_date` is reached and the security deposit is paid, it becomes `active`. The `Unit` is marked as `is_occupied: true`.

2.  **Automated Rent Collection:**
    - A monthly background job (e.g., using Sidekiq) runs for all `active` leases.
    - For each lease, it creates a `RentPayment` record for the upcoming month with `status: 'due'`.
    - It sends an email notification to the tenant that rent is due.
    - If the `due_date` passes and `status` is not `paid`, another job marks it as `late` and can apply late fees.

3.  **Maintenance Workflow:**
    - A tenant creates a `MaintenanceRequest` from their dashboard, attaching photos.
    - The landlord of the associated `Property` is notified.
    - The landlord can update the `status` of the request (e.g., to `in_progress` when work begins).
    - The landlord or tenant can add comments (would require a `Comment` model polymorphic to `MaintenanceRequest`).
    - Once work is done, the landlord marks it `completed`.

4.  **Lease Termination/Expiration:**
    - When a `Lease` `end_date` is approaching, the system notifies the landlord to take action (renew or terminate).
    - When a lease ends, its `status` becomes `expired` and the associated `Unit` is marked `is_occupied: false`.

5.  **Financial Reporting:**
    - Landlords have a dashboard that aggregates data.
    - **Income:** Sum of `RentPayment` records where `status: 'paid'`.
    - **Expenses:** Sum of `Expense` records.
    - **Profit/Loss:** Income - Expenses per property.
    - **Occupancy Rate:** (Number of occupied units / Total number of units) * 100.
