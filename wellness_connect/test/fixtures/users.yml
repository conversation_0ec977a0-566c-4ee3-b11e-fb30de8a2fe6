# Read about fixtures at https://api.rubyonrails.org/classes/ActiveRecord/FixtureSet.html

patient_user:
  email: <EMAIL>
  encrypted_password: <%= Devise::Encryptor.digest(User, 'password123') %>
  first_name: <PERSON>
  last_name: <PERSON><PERSON>
  role: 0 # patient
  time_zone: America/New_York

provider_user:
  email: <EMAIL>
  encrypted_password: <%= Devise::Encryptor.digest(User, 'password123') %>
  first_name: Dr. <PERSON>
  last_name: Smith
  role: 1 # provider
  time_zone: America/Los_Angeles

admin_user:
  email: <EMAIL>
  encrypted_password: <%= Devise::Encryptor.digest(User, 'password123') %>
  first_name: Admin
  last_name: User
  role: 2 # admin
  time_zone: UTC
