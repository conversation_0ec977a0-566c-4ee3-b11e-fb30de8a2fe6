<%= form_with(model: provider_profile, class: "bg-white shadow rounded-lg p-6") do |form| %>
  <% if provider_profile.errors.any? %>
    <div class="bg-red-100 border border-red-400 text-red-700 px-4 py-3 rounded mb-4">
      <h2 class="font-bold"><%= pluralize(provider_profile.errors.count, "error") %> prohibited this provider profile from being saved:</h2>
      <ul class="list-disc list-inside">
        <% provider_profile.errors.each do |error| %>
          <li><%= error.full_message %></li>
        <% end %>
      </ul>
    </div>
  <% end %>

  <div class="mb-4">
    <%= form.label :specialty, class: "block text-gray-700 font-bold mb-2" %>
    <%= form.text_field :specialty, class: "w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" %>
  </div>

  <div class="mb-4">
    <%= form.label :credentials, class: "block text-gray-700 font-bold mb-2" %>
    <%= form.text_field :credentials, class: "w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" %>
  </div>

  <div class="mb-4">
    <%= form.label :bio, class: "block text-gray-700 font-bold mb-2" %>
    <%= form.text_area :bio, rows: 5, class: "w-full px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" %>
  </div>

  <div class="mb-6">
    <%= form.label :consultation_rate, class: "block text-gray-700 font-bold mb-2" %>
    <div class="flex items-center">
      <span class="text-gray-700 mr-2">$</span>
      <%= form.number_field :consultation_rate, step: 0.01, class: "flex-1 px-3 py-2 border border-gray-300 rounded focus:outline-none focus:ring-2 focus:ring-blue-500" %>
    </div>
  </div>

  <div class="flex gap-2">
    <%= form.submit class: "bg-blue-500 text-white px-6 py-2 rounded hover:bg-blue-600 cursor-pointer" %>
    <%= link_to "Cancel", provider_profiles_path, class: "bg-gray-500 text-white px-6 py-2 rounded hover:bg-gray-600" %>
  </div>
<% end %>
