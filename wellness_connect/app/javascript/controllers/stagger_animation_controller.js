import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="stagger-animation"
export default class extends Controller {
  static targets = ["item"]
  static values = {
    delay: { type: Number, default: 100 },
    threshold: { type: Number, default: 0.1 }
  }

  connect() {
    // Check for reduced motion preference
    this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
    
    if (this.prefersReducedMotion) {
      // Show all items immediately
      this.itemTargets.forEach(item => {
        item.style.opacity = '1'
        item.style.transform = 'none'
      })
      return
    }

    // Set initial state for all items
    this.itemTargets.forEach(item => {
      item.style.opacity = '0'
      item.style.transform = 'translateY(20px)'
    })

    // Create intersection observer
    this.observer = new IntersectionObserver(
      (entries) => this.handleIntersection(entries),
      { threshold: this.thresholdValue }
    )

    this.observer.observe(this.element)
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        this.animateItems()
        this.observer.unobserve(this.element)
      }
    })
  }

  animateItems() {
    this.itemTargets.forEach((item, index) => {
      setTimeout(() => {
        item.style.transition = 'opacity 0.5s ease-out, transform 0.5s ease-out'
        item.style.opacity = '1'
        item.style.transform = 'translateY(0)'
      }, index * this.delayValue)
    })
  }
}

