import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="hover-animation"
export default class extends Controller {
  static values = {
    scale: { type: Number, default: 1.05 },
    translateY: { type: Number, default: -5 },
    duration: { type: Number, default: 300 }
  }

  connect() {
    // Check for reduced motion preference
    this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
    
    if (this.prefersReducedMotion) {
      return
    }

    this.element.style.transition = `transform ${this.durationValue}ms ease-out, box-shadow ${this.durationValue}ms ease-out`
  }

  mouseenter() {
    if (this.prefersReducedMotion) return
    
    this.element.style.transform = `scale(${this.scaleValue}) translateY(${this.translateYValue}px)`
  }

  mouseleave() {
    if (this.prefersReducedMotion) return
    
    this.element.style.transform = 'scale(1) translateY(0)'
  }
}

