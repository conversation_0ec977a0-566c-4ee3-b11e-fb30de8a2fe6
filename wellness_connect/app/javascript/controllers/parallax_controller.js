import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="parallax"
export default class extends Controller {
  static values = {
    speed: { type: Number, default: 0.5 },
    direction: { type: String, default: 'vertical' }
  }

  connect() {
    // Check for reduced motion preference
    this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
    
    if (this.prefersReducedMotion) {
      return
    }

    this.handleScroll = this.handleScroll.bind(this)
    window.addEventListener('scroll', this.handleScroll, { passive: true })
    this.handleScroll() // Initial position
  }

  disconnect() {
    window.removeEventListener('scroll', this.handleScroll)
  }

  handleScroll() {
    const scrolled = window.pageYOffset
    const rect = this.element.getBoundingClientRect()
    const elementTop = rect.top + scrolled
    const elementHeight = rect.height
    const windowHeight = window.innerHeight

    // Only apply parallax when element is in viewport
    if (scrolled + windowHeight > elementTop && scrolled < elementTop + elementHeight) {
      const offset = (scrolled - elementTop) * this.speedValue

      if (this.directionValue === 'vertical') {
        this.element.style.transform = `translateY(${offset}px)`
      } else if (this.directionValue === 'horizontal') {
        this.element.style.transform = `translateX(${offset}px)`
      }
    }
  }
}

