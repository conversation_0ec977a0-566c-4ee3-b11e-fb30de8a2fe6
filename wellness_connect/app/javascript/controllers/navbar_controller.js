import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="navbar"
export default class extends Controller {
  static targets = ["menu", "hamburger"]
  static values = {
    scrollThreshold: { type: Number, default: 50 }
  }

  connect() {
    this.handleScroll = this.handleScroll.bind(this)
    window.addEventListener('scroll', this.handleScroll, { passive: true })
    this.handleScroll() // Initial state
    this.menuOpen = false
  }

  disconnect() {
    window.removeEventListener('scroll', this.handleScroll)
  }

  handleScroll() {
    const scrolled = window.pageYOffset

    if (scrolled > this.scrollThresholdValue) {
      this.element.classList.add('navbar-scrolled')
      this.element.classList.remove('navbar-top')
    } else {
      this.element.classList.remove('navbar-scrolled')
      this.element.classList.add('navbar-top')
    }
  }

  toggleMenu() {
    this.menuOpen = !this.menuOpen
    
    if (this.hasMenuTarget) {
      if (this.menuOpen) {
        this.menuTarget.classList.remove('hidden')
        this.menuTarget.classList.add('flex')
        // Animate in
        setTimeout(() => {
          this.menuTarget.classList.add('menu-open')
        }, 10)
      } else {
        this.menuTarget.classList.remove('menu-open')
        // Wait for animation to complete before hiding
        setTimeout(() => {
          this.menuTarget.classList.add('hidden')
          this.menuTarget.classList.remove('flex')
        }, 300)
      }
    }

    // Toggle hamburger icon
    if (this.hasHamburgerTarget) {
      this.hamburgerTarget.classList.toggle('active')
    }

    // Prevent body scroll when menu is open
    document.body.style.overflow = this.menuOpen ? 'hidden' : ''
  }

  closeMenu() {
    if (this.menuOpen) {
      this.toggleMenu()
    }
  }

  // Smooth scroll to anchor
  scrollTo(event) {
    const href = event.currentTarget.getAttribute('href')
    
    if (href && href.startsWith('#')) {
      event.preventDefault()
      const target = document.querySelector(href)
      
      if (target) {
        // Close mobile menu if open
        this.closeMenu()
        
        // Smooth scroll to target
        const navbarHeight = this.element.offsetHeight
        const targetPosition = target.offsetTop - navbarHeight
        
        window.scrollTo({
          top: targetPosition,
          behavior: 'smooth'
        })
      }
    }
  }
}

