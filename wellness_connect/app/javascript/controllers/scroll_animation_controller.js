import { Controller } from "@hotwired/stimulus"

// Connects to data-controller="scroll-animation"
export default class extends Controller {
  static values = {
    threshold: { type: Number, default: 0.1 },
    rootMargin: { type: String, default: "0px" },
    animationClass: { type: String, default: "animate-fade-in" },
    delay: { type: Number, default: 0 },
    once: { type: Boolean, default: true }
  }

  connect() {
    // Check for reduced motion preference
    this.prefersReducedMotion = window.matchMedia('(prefers-reduced-motion: reduce)').matches
    
    if (this.prefersReducedMotion) {
      // Skip animations if user prefers reduced motion
      this.element.classList.add('opacity-100')
      return
    }

    // Set initial state
    this.element.style.opacity = '0'
    this.element.style.transform = this.getInitialTransform()
    
    // Create intersection observer
    this.observer = new IntersectionObserver(
      (entries) => this.handleIntersection(entries),
      {
        threshold: this.thresholdValue,
        rootMargin: this.rootMarginValue
      }
    )

    this.observer.observe(this.element)
  }

  disconnect() {
    if (this.observer) {
      this.observer.disconnect()
    }
  }

  handleIntersection(entries) {
    entries.forEach(entry => {
      if (entry.isIntersecting) {
        // Apply delay if specified
        setTimeout(() => {
          this.animate()
        }, this.delayValue)

        // Unobserve if animation should only happen once
        if (this.onceValue) {
          this.observer.unobserve(this.element)
        }
      } else if (!this.onceValue) {
        // Reset animation if it should repeat
        this.reset()
      }
    })
  }

  animate() {
    this.element.style.transition = 'opacity 0.6s ease-out, transform 0.6s ease-out'
    this.element.style.opacity = '1'
    this.element.style.transform = 'translate(0, 0) scale(1)'
    
    // Add custom animation class if specified
    if (this.animationClassValue) {
      this.element.classList.add(this.animationClassValue)
    }
  }

  reset() {
    this.element.style.opacity = '0'
    this.element.style.transform = this.getInitialTransform()
    
    if (this.animationClassValue) {
      this.element.classList.remove(this.animationClassValue)
    }
  }

  getInitialTransform() {
    // Get animation direction from data attribute
    const direction = this.element.dataset.animationDirection || 'up'
    
    switch(direction) {
      case 'up':
        return 'translateY(30px)'
      case 'down':
        return 'translateY(-30px)'
      case 'left':
        return 'translateX(30px)'
      case 'right':
        return 'translateX(-30px)'
      case 'scale':
        return 'scale(0.95)'
      default:
        return 'translateY(30px)'
    }
  }
}

