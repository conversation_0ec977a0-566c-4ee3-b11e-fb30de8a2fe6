# Appointment Reminders System

## Overview

The appointment reminder system automatically sends email notifications to both patients (clients) and service providers 24 hours before scheduled appointments.

## Components

### 1. AppointmentReminderJob

Located in `app/jobs/appointment_reminder_job.rb`

- **Purpose**: Background job that finds appointments in the 23-25 hour window and sends reminder emails
- **Queue**: Default queue (can be configured via Solid Queue)
- **Frequency**: Designed to run hourly (every hour between 23-25 hours captures the 24-hour target)

### 2. AppointmentMailer

Located in `app/mailers/appointment_mailer.rb`

Contains two reminder methods:
- `reminder_to_patient(appointment)` - Sends reminder to the client
- `reminder_to_provider(appointment)` - Sends reminder to the service provider

### 3. Email Templates

Located in `app/views/appointment_mailer/`:
- `reminder_to_patient.html.erb` - HTML version for patients
- `reminder_to_patient.text.erb` - Plain text version for patients
- `reminder_to_provider.html.erb` - HTML version for providers
- `reminder_to_provider.text.erb` - Plain text version for providers

All templates support:
- Multipart emails (HTML + text)
- Timezone conversion
- Video session conditional display
- Professional styling with inline CSS

## Production Setup

### Step 1: Configure Environment Variables

Add to your production environment configuration:

```bash
# Email configuration
MAILER_FROM_EMAIL=<EMAIL>

# SMTP settings (example with SendGrid)
SMTP_ADDRESS=smtp.sendgrid.net
SMTP_PORT=587
SMTP_USERNAME=apikey
SMTP_PASSWORD=your_sendgrid_api_key
SMTP_DOMAIN=yourdomain.com
```

### Step 2: Update Production Email Settings

In `config/environments/production.rb`, ensure you have:

```ruby
config.action_mailer.delivery_method = :smtp
config.action_mailer.perform_deliveries = true
config.action_mailer.raise_delivery_errors = true

config.action_mailer.smtp_settings = {
  address: ENV['SMTP_ADDRESS'],
  port: ENV['SMTP_PORT'],
  domain: ENV['SMTP_DOMAIN'],
  user_name: ENV['SMTP_USERNAME'],
  password: ENV['SMTP_PASSWORD'],
  authentication: :plain,
  enable_starttls_auto: true
}

config.action_mailer.default_url_options = {
  host: 'yourdomain.com',
  protocol: 'https'
}
```

### Step 3: Set Up Recurring Job

#### Option A: Using Cron (Recommended)

Add to your server's crontab:

```bash
# Run appointment reminders every hour
0 * * * * cd /path/to/wellness_connect && RAILS_ENV=production bin/rails runner 'AppointmentReminderJob.perform_now'
```

#### Option B: Using whenever gem

Add to your `Gemfile`:
```ruby
gem 'whenever', require: false
```

Create `config/schedule.rb`:
```ruby
every 1.hour do
  runner "AppointmentReminderJob.perform_now"
end
```

Deploy:
```bash
bundle install
whenever --update-crontab
```

#### Option C: Using Solid Queue Recurring Jobs

Create `config/recurring.yml`:
```yaml
production:
  appointment_reminders:
    class: AppointmentReminderJob
    queue: default
    schedule: "0 * * * *"  # Every hour at minute 0
```

### Step 4: Verify Solid Queue is Running

Ensure Solid Queue is running in production:

```bash
# Via Procfile or systemd
bin/jobs
```

Or add to your `Procfile`:
```
worker: bundle exec rake solid_queue:start
```

### Step 5: Monitor Email Delivery

Check logs for reminder job execution:

```bash
# Production logs
tail -f log/production.log | grep "AppointmentReminderJob"
```

Look for log messages:
- "Sent reminders for Appointment #X"
- "Appointment reminder job completed. Sent reminders for X appointments."

## Testing

### Manual Test in Production Console

```ruby
# Rails console
rails console production

# Find an upcoming appointment
appointment = Appointment.find_by(status: :scheduled)

# Test patient reminder
AppointmentMailer.reminder_to_patient(appointment).deliver_now

# Test provider reminder
AppointmentMailer.reminder_to_provider(appointment).deliver_now
```

### Automated Test Script

Run the test script to verify everything works:

```bash
# Development/Staging
bin/rails runner tmp/test_appointment_reminders.rb

# Should show:
# ✅ Reminder job execution: PASSED
# ✅ Email template rendering: PASSED
# ✅ Email queueing: PASSED
# ✅ Time window filtering: PASSED
```

## Email Providers

### Recommended SMTP Providers

1. **SendGrid** (recommended)
   - Free tier: 100 emails/day
   - Pricing: $15/mo for 40K emails
   - Excellent deliverability

2. **Postmark**
   - Transactional email specialist
   - 100 emails/mo free
   - $10/mo for 10K emails

3. **AWS SES**
   - Very cost-effective ($0.10 per 1K emails)
   - Requires AWS setup
   - Excellent for high volume

4. **Mailgun**
   - 5K emails/mo free
   - Good for developers

## Monitoring

### Key Metrics to Track

1. **Email Delivery Rate**: % of emails successfully delivered
2. **Bounce Rate**: % of emails that bounced (keep under 5%)
3. **Spam Complaints**: Keep under 0.1%
4. **Job Execution Time**: Should complete in <5 seconds per appointment
5. **Email Queue Size**: Monitor for backlogs

### Alerting

Set up alerts for:
- Email delivery failures
- Job execution failures
- High bounce rates
- SMTP authentication errors

## Troubleshooting

### Common Issues

1. **Emails not sending**
   - Check SMTP credentials
   - Verify Solid Queue is running
   - Check production logs
   - Test SMTP connection manually

2. **Wrong timezone in emails**
   - Ensure user.time_zone is set
   - Check Time.zone configuration
   - Verify timezone database is updated

3. **Job not running hourly**
   - Verify cron/whenever is configured
   - Check crontab: `crontab -l`
   - Check system logs: `grep CRON /var/log/syslog`

4. **Emails going to spam**
   - Set up SPF, DKIM, and DMARC records
   - Use a reputable SMTP provider
   - Avoid spam trigger words in subject/body
   - Maintain good sender reputation

### Debug Mode

To debug email issues:

```ruby
# In production console
ActionMailer::Base.logger.level = Logger::DEBUG

# Send test email
AppointmentMailer.reminder_to_patient(appointment).deliver_now

# Check detailed SMTP logs
```

## Future Enhancements

Potential improvements:
1. **SMS reminders** - Add Twilio integration for text reminders
2. **Custom timing** - Allow users to configure reminder timing (12h, 24h, 48h)
3. **Multiple reminders** - Send follow-up reminders (24h and 1h before)
4. **Cancellation confirmations** - Automatic emails when appointments are cancelled
5. **Rescheduling notifications** - Automatic emails when appointments are rescheduled
6. **Summary reports** - Daily/weekly summary of upcoming appointments

## Support

For issues or questions:
- Check logs: `log/production.log`
- Review email provider dashboard
- Check Solid Queue status
- Contact platform administrator
