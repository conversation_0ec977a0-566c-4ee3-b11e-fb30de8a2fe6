# Comprehensive Prompt for Building the Property Management Automation Platform

## 1. Project Goal & Tech Stack

**Primary Goal:** Develop a complete Ruby on Rails 8 application, "Propely," that allows landlords to manage their properties, units, leases, and finances, while providing tenants with a portal for payments and maintenance requests.

**Core Technologies:**
- **Backend:** Ruby on Rails 8
- **Database:** PostgreSQL
- **Frontend:** Hotwire (Turbo & Stimulus), TailwindCSS
- **Authentication:** Devise gem
- **Payments:** Stripe gem
- **Background Jobs:** Sidekiq
- **File Uploads:** Active Storage

## 2. Core Features for MVP

- **User Authentication:** Secure sign-up and login for Landlords and Tenants using Devise.
- **Property & Unit Management:** Landlords can perform full CRUD operations on their properties and the units within them.
- **Lease Management:** Landlords can create and manage leases, associating a tenant with a specific unit and defining rent terms.
- **Automated Rent Invoicing:** A recurring background job generates monthly rent payment records for all active leases.
- **Tenant Portal:** Tenants can view their lease, see payment history, and make rent payments.
- **Maintenance Workflow:** Tenants can submit maintenance requests (with photo uploads), and landlords can track and update their status.
- **Landlord Dashboard:** A dashboard providing landlords with a financial overview (income vs. expenses) and occupancy rates.

## 3. Data Schema

The complete data schema, including all models, attributes, and associations, is defined in the accompanying file: **`property_management_automation.md`**. You must adhere strictly to this data model to ensure relational integrity.

## 4. Step-by-Step Implementation Plan

Please proceed sequentially through the following steps, confirming the completion of each major stage.

**Step 1: Initial Application Setup**
1.  Generate a new Rails 8 application:
    ```bash
    rails new propely --database=postgresql -c tailwind
    ```
2.  Navigate into the `propely` directory.
3.  Add necessary gems to the `Gemfile`:
    ```ruby
    gem 'devise'
    gem 'pundit'
    gem 'stripe'
    ```
4.  Run `bundle install`, create the database (`rails db:create`), and set up Sidekiq.

**Step 2: Authentication & Authorization**
1.  Set up Devise for the `User` model.
2.  Add a `role` column to the `users` table (enum: `landlord`, `tenant`, `admin`).
3.  Set up Pundit for authorization. Policies should ensure landlords can only manage their own properties and tenants can only view their own lease information.

**Step 3: Model & Migration Generation**
1.  Using the `property_management_automation.md` file as a reference, generate all required models and migrations. For example:
    ```bash
    rails g model Property landlord:references name:string address:string
    rails g model Unit property:references unit_number:string bedrooms:integer is_occupied:boolean
    rails g model Lease unit:references tenant:references start_date:date end_date:date monthly_rent:decimal status:integer
    # ... and so on for all other models.
    ```
2.  Implement all `has_many`, `belongs_to`, etc., associations in the `app/models/*.rb` files.
3.  Run `rails db:migrate`.

**Step 4: Landlord-Side Features (Controllers & Views)**
1.  Implement full CRUD functionality for `Properties` and `Units`.
2.  Implement full CRUD for `Leases`, including a way to invite a tenant by email.
3.  Create views to manage `MaintenanceRequests` and `Expenses`.
4.  Build the main landlord dashboard, calculating and displaying key metrics like income, expenses, and occupancy.

**Step 5: Tenant-Side Features (Controllers & Views)**
1.  Create a tenant-specific dashboard to view lease details and a list of `RentPayments`.
2.  Implement the payment flow using Stripe to pay for outstanding rent.
3.  Implement full CRUD for `MaintenanceRequests`, including using Active Storage for photo uploads.

## 6. Core Business Logic (Scheduled Rake Task)

1.  Create a Rake task (e.g., in `lib/tasks/rent.rake`).
2.  The task (e.g., `rent:generate_invoices`) should iterate through all `active` leases and create a new `RentPayment` record for the upcoming month with a `status` of `due`.
3.  **Note for Production:** This task is intended to be run by a system scheduler (like `cron` or a platform-specific equivalent like Render Cron Jobs) on the first day of each month.

**Step 7: Styling**
- Throughout the development process, use TailwindCSS utility classes to style all views. Ensure the UI is clean, intuitive, and responsive.

Please begin with Step 1.
